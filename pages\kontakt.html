<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kontakt - Balkan Recruiters | Personalvermittlung vom Balkan</title>
    <meta name="description" content="Kontaktieren Sie Balkan Recruiters für die Vermittlung qualifizierter Fachkräfte aus dem Westbalkan. Wir helfen Ihnen den passenden Mitarbeiter zu finden.">
    <meta name="keywords" content="Kontakt Balkan Recruiters, Fachkräfte Balkan, Personalvermittlung kontaktieren">
    <link rel="canonical" href="https://www.balkanrecruiters.de/pages/kontakt.html" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="../favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../favicon/favicon-16x16.png">
    <link rel="manifest" href="../favicon/site.webmanifest">
    
    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="Kontakt - Balkan Recruiters | Personalvermittlung vom Balkan" />
    <meta property="og:description" content="Kontaktieren Sie Balkan Recruiters für die Vermittlung qualifizierter Fachkräfte aus dem Westbalkan." />
    <meta property="og:url" content="https://www.balkanrecruiters.de/pages/kontakt.html" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://www.balkanrecruiters.de/photos/logo.png" />
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Kontakt - Balkan Recruiters | Personalvermittlung vom Balkan" />
    <meta name="twitter:description" content="Kontaktieren Sie Balkan Recruiters für die Vermittlung qualifizierter Fachkräfte aus dem Westbalkan." />
    <meta name="twitter:image" content="https://www.balkanrecruiters.de/photos/logo.png" />
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
    
    <!-- Structured data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "name": "Kontakt - Balkan Recruiters",
      "description": "Kontaktieren Sie uns für qualifizierte Arbeitskräfte vom Balkan",
      "mainEntity": {
        "@type": "Organization",
        "name": "Balkan Recruiters",
        "url": "https://www.balkanrecruiters.de",
        "logo": "https://www.balkanrecruiters.de/photos/logo.png",
        "telephone": "0210 273 920 34",
        "email": "<EMAIL>",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Husein Kapetana Gradaščevića 54",
          "addressLocality": "Jelah",
          "postalCode": "74264",
          "addressCountry": "BA"
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "telephone": "0210 273 920 34",
          "email": "<EMAIL>",
          "availableLanguage": ["German", "English", "Bosnian", "Croatian", "Serbian"]
        }
      }
    }
    </script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        /* Optional: Add smooth transitions for dropdowns */
        .dropdown-transition {
            transition: opacity 0.15s ease-out, transform 0.15s ease-out;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#023679',
                        secondary: '#0369a1',
                        accent: '#3b82f6'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50" x-data="{ mobileMenuOpen: false }">
    <header>
        <nav class="bg-white shadow-sm fixed w-full top-0 z-50">
            <div class="container mx-auto px-4 py-5">
                <!-- Mobile Menu Button -->
                <div class="flex items-center justify-between lg:hidden">
                    <a href="../index.html">
                        <img src="../photos/logo.png" alt="BalkanRecruiters Logo" class="h-12">
                    </a>
                    <button class="text-gray-600 hover:text-primary" @click="mobileMenuOpen = !mobileMenuOpen">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:flex items-center">
                    <a href="../index.html" class="absolute left-4">
                        <img src="../photos/logo.png" alt="BalkanRecruiters Logo" class="h-12">
                    </a>
                    <ul class="flex space-x-8 mx-auto">
                        <li><a href="dienstleistungen.html" class="nav-link text-gray-700">Dienstleistungen</a></li>
                        <li><a href="system.html" class="nav-link text-gray-700">Plattform</a></li>
                        <li><a href="prozess.html" class="nav-link text-gray-700">Prozess</a></li>
                        <li><a href="ueber-uns.html" class="nav-link text-gray-700">Über uns</a></li>
                        <li><a href="kontakt.html" class="nav-link text-gray-700 active">Kontakt</a></li>
                    </ul>
                    <div class="flex items-center space-x-6 absolute right-4">
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-1 text-gray-700 hover:text-primary">
                                <span class="font-medium text-primary">DE</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div x-cloak x-show="open" x-transition.opacity @click.away="open = false" 
                                 class="absolute right-0 mt-2 py-2 w-16 bg-white rounded-lg shadow-xl dropdown-transition">
                                <a href="https://balkanrecruiters.com/pages/kontakt.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 text-center">BS</a>
                            </div>
                        </div>
                        <a href="https://crm.ssc.ba/" target="_blank" class="text-primary hover:text-white border-2 border-primary hover:bg-primary px-4 py-2 rounded-md transition-all duration-200">Partner Login</a>
                    </div>
                </div>

                <!-- Mobile Menu -->
                <div x-show="mobileMenuOpen" class="lg:hidden">
                    <ul class="mt-4 space-y-2">
                        <li><a href="dienstleistungen.html" class="block text-gray-700 hover:text-primary py-2">Dienstleistungen</a></li>
                        <li><a href="system.html" class="block text-gray-700 hover:text-primary py-2">Plattform</a></li>
                        <li><a href="prozess.html" class="block text-gray-700 hover:text-primary py-2">Prozess</a></li>
                        <li><a href="ueber-uns.html" class="block text-gray-700 hover:text-primary py-2">Über uns</a></li>
                        <li><a href="kontakt.html" class="block text-gray-700 hover:text-primary py-2">Kontakt</a></li>
                    </ul>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex space-x-4">
                            <a href="../../bs/pages/kontakt.html" class="text-gray-700 hover:text-primary font-medium">BS</a>
                            <a href="../../en/pages/contact.html" class="text-gray-700 hover:text-primary font-medium">EN</a>
                        </div>
                        <a href="https://crm.ssc.ba/" target="_blank" class="text-primary hover:text-white border-2 border-primary hover:bg-primary px-4 py-2 rounded-md transition-all duration-200">Partner Login</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Page Header -->
        <section class="bg-primary text-white py-16 mt-[60px]">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <h1 class="text-3xl md:text-4xl font-bold mb-4">Kontaktieren Sie uns</h1>
                    <p class="text-lg md:text-xl text-white/90">
                        Haben Sie Fragen oder suchen Sie qualifizierte Arbeitskräfte? Kontaktieren Sie uns - wir helfen Ihnen gerne weiter!
                    </p>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <section class="py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <div class="grid md:grid-cols-2 gap-12">
                        <!-- Contact Form -->
                        <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300">
                            <h2 class="text-2xl font-semibold text-primary mb-6">Senden Sie uns eine Nachricht</h2>
                            <form class="space-y-6" id="contactForm" onsubmit="handleSubmit(event)">
                                <div>
                                    <label for="name" class="block text-gray-700 mb-2 font-medium">Ihr Name</label>
                                    <input type="text" id="name" name="name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all">
                                </div>
                                <div>
                                    <label for="email" class="block text-gray-700 mb-2 font-medium">E-Mail-Adresse</label>
                                    <input type="email" id="email" name="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all">
                                </div>
                                <div>
                                    <label for="message" class="block text-gray-700 mb-2 font-medium">Ihre Nachricht</label>
                                    <textarea id="message" name="message" required rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"></textarea>
                                </div>
                                <div id="statusMessage" class="text-center hidden">
                                    <p class="text-green-600"></p>
                                </div>
                                <button type="submit" class="w-full bg-primary text-white py-3 px-6 rounded-lg hover:bg-primary/90 transition-all duration-300 font-medium">
                                    Nachricht senden
                                </button>
                            </form>
                        </div>

                        <!-- Contact Information -->
                        <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300">
                            <h2 class="text-2xl font-semibold text-primary mb-8">Kontaktinformationen</h2>
                            <div class="space-y-8">
                                <!-- Address -->
                                <div class="flex items-start space-x-4">
                                    <div class="text-primary mt-1">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-2">Adresse</h3>
                                        <p class="text-gray-600">
                                            Husein Kapetana Gradaščevića 54<br>
                                            74264 Jelah<br>
                                            Bosnien und Herzegowina
                                        </p>
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class="flex items-start space-x-4">
                                    <div class="text-primary mt-1">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-2">E-Mail</h3>
                                        <a href="mailto:<EMAIL>" class="text-primary hover:text-primary/80"><EMAIL></a>
                                    </div>
                                </div>

                                <!-- Opening Hours -->
                                <div class="flex items-start space-x-4">
                                    <div class="text-primary mt-1">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 mb-2">Öffnungszeiten</h3>
                                        <p class="text-gray-600">
                                            Montag - Freitag: 09:00 - 17:00<br>
                                            Samstag - Sonntag: Geschlossen
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Map -->
                            <div class="mt-8">
                                <div class="relative w-full h-64 rounded-lg overflow-hidden">
                                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d375.14382617982164!2d17.96443520003191!3d44.655125425368254!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x475e87dbb42b23db%3A0xbba228c4971cbe5!2sSSC%20d.o.o.%20-%20Sales%20%26%20Service%20Center!5e1!3m2!1sde!2sba!4v1741732192173!5m2!1sde!2sba" 
                                            width="100%" 
                                            height="100%" 
                                            style="border:0;" 
                                            allowfullscreen="" 
                                            loading="lazy" 
                                            referrerpolicy="no-referrer-when-downgrade">
                                    </iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-900 to-primary text-white pt-16 pb-8">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <!-- Company Info -->
                <div class="space-y-4">
                    <img src="../photos/logo.png" alt="BalkanRecruiters Logo" class="h-12 mb-4">
                    <p class="text-gray-300">Ihr vertrauenswürdiger Partner für die Suche nach qualifizierten Arbeitskräften vom Balkan.</p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Schnelllinks</h4>
                    <ul class="space-y-2">
                        <li><a href="dienstleistungen.html" class="text-gray-300 hover:text-white transition-colors">Dienstleistungen</a></li>
                        <li><a href="system.html" class="text-gray-300 hover:text-white transition-colors">Plattform</a></li>
                        <li><a href="prozess.html" class="text-gray-300 hover:text-white transition-colors">Prozess</a></li>
                        <li><a href="ueber-uns.html" class="text-gray-300 hover:text-white transition-colors">Über uns</a></li>
                        <li><a href="kontakt.html" class="text-gray-300 hover:text-white transition-colors">Kontakt</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Rechtliches</h4>
                    <ul class="space-y-2">
                        <li><a href="datenschutz.html" class="text-gray-300 hover:text-white transition-colors">Datenschutz</a></li>
                        <li><a href="impressum.html" class="text-gray-300 hover:text-white transition-colors">Impressum</a></li>
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Kontakt</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-start space-x-2">
                            <svg class="w-5 h-5 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <div class="flex flex-col">
                                <span>Husein Kapetana Gradaščevića 54</span>
                                <span>74264 Jelah</span>
                                <span>Bosnien und Herzegowina</span>
                            </div>
                        </li>
                        <li class="flex items-center space-x-2">
                            <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 pt-8 text-center text-gray-400">
                <p>&copy;2025 BalkanRecruiters. Alle Rechte vorbehalten.</p>
            </div>
        </div>
    </footer>

    <script>
        async function handleSubmit(event) {
            event.preventDefault();
            const form = event.target;
            const statusMessage = document.getElementById('statusMessage');
            const statusText = statusMessage.querySelector('p');
            
            try {
                const response = await fetch('/api/sendEmail', {  // Ovo će sad pozivati Express endpoint
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: form.name.value,
                        email: form.email.value,
                        message: form.message.value
                    })
                });

                if (response.ok) {
                    statusText.textContent = 'Nachricht erfolgreich gesendet!';
                    statusText.className = 'text-green-600';
                    form.reset();
                } else {
                    statusText.textContent = 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';
                    statusText.className = 'text-red-600';
                }
            } catch (error) {
                statusText.textContent = 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';
                statusText.className = 'text-red-600';
            }
            
            statusMessage.classList.remove('hidden');
            setTimeout(() => {
                statusMessage.classList.add('hidden');
            }, 5000);
        }
    </script>
</body>
</html>