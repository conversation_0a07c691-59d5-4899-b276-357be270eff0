/* Add this at the top of your CSS file */
[x-cloak] { 
    display: none !important;
}

/* Base styles */
body {
    font-family: 'Inter', sans-serif;
}

:root {
    --primary-color: #023679;
}

/* Navigation styles */
.nav-link {
    position: relative;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.nav-link:hover::after {
    transform-origin: bottom left;
    transform: scaleX(1);
}

.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    transform: scaleX(1);
}

/* Layout helpers */
.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

/* Partners carousel animation */
@keyframes scroll-slow {
    0% { transform: translateX(0); }
    100% { transform: translateX(calc(-100% / 2)); }
}

@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(calc(-100% / 2)); }
}

.animate-scroll {
    animation: scroll 30s linear infinite;
}

.animate-scroll-slow {
    animation: scroll-slow 45s linear infinite;
}

.logos-scroll:hover {
    animation-play-state: paused;
}

/* Hover effects */
.hover-shine {
    position: relative;
    overflow: hidden;
}

.hover-shine::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
        120deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: 0.5s;
}

.hover-shine:hover::after {
    left: 100%;
}

/* Background patterns */
.pattern-grid {
    background-image: linear-gradient(currentColor 1px, transparent 1px),
                    linear-gradient(to right, currentColor 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Card transitions */
.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

/* Stats section styles */
.stat-circle:hover svg {
    transform: scale(1.1) rotate(5deg);
}

.trust-section:hover .connector-line {
    transform: scaleX(1);
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.stat-circle {
    animation: fadeInScale 0.6s ease-out forwards;
}

.stat-circle:nth-child(2) {
    animation-delay: 0.2s;
}

.stat-circle:nth-child(3) {
    animation-delay: 0.4s;
}

.stat-circle:nth-child(4) {
    animation-delay: 0.6s;
}

/* Journey map styles */
.journey-map {
    position: relative;
    min-height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
}

.journey-step {
    position: relative;
    padding: 2rem 0;
    display: flex;
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.journey-step.visible {
    opacity: 1;
    transform: translateY(0);
}

.journey-step:nth-child(odd) {
    justify-content: flex-start;
}

.journey-step:nth-child(even) {
    justify-content: flex-end;
}

.journey-step .flex {
    gap: 3rem;
    align-items: flex-start;
    width: auto;
    max-width: 850px;
    display: flex;
}

.journey-step:nth-child(even) .flex {
    flex-direction: row-reverse;
}

.step-number {
    width: 4rem;
    height: 4rem;
    min-width: 4rem;
    min-height: 4rem;
    background: white;
    border: 3px solid var(--primary-color);
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    color: var(--primary-color);
    position: relative;
    z-index: 3;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Enhanced step number hover effect */
.step-number:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #0369a1;
    background-color: #f8fafc;
}

/* Process page content card styles */
.content-card {
    width: 1000px;
    background: white;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(2, 54, 121, 0.1);
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.content-text {
    flex: 1;
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.content-image {
    flex: 1;
    min-height: 300px;
    background-size: cover;
    background-position: center;
}

/* Hero section styling */
@media (max-width: 768px) {
    .hero-section img {
        object-position: left center;
    }
}

.hero-section {
    margin-top: -100px; /* Compensate for fixed header */
}

/* Space between steps */
.space-y-32 {
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}

/* Pattern elements */
.pattern-grid {
    background-image: linear-gradient(var(--primary-color) 1px, transparent 1px),
                      linear-gradient(90deg, var(--primary-color) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Process page responsive styles */
@media (max-width: 1280px) {
    .content-card {
        width: 800px;
    }
}

@media (max-width: 1024px) {
    .content-card {
        width: 700px;
    }
}

@media (max-width: 768px) {
    .journey-step,
    .journey-step:nth-child(even) {
        justify-content: flex-start;
    }
    
    .journey-step .flex,
    .journey-step:nth-child(even) .flex {
        flex-direction: row;
        width: 100%;
        gap: 1rem;
    }
    
    .content-card {
        width: calc(100% - 5rem);
        flex-direction: column !important;
        margin: 0;
    }
    
    .step-number {
        margin-top: 1rem;
        flex-shrink: 0;
    }
    
    .content-image {
        height: 200px;
        width: 100%;
    }
    
    .content-text {
        padding: 1.5rem;
    }
    
    .space-y-32 {
        margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
        margin-bottom: calc(3rem * var(--tw-space-y-reverse));
    }
}

/* Hero content animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content {
    animation: fadeInUp 0.8s ease forwards;
}

/* Alpine.js transitions */
.dropdown-transition {
    transition: opacity 0.15s ease-out, transform 0.15s ease-out;
}

/* Scroll down arrow animation */
.scroll-arrow {
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.scroll-arrow:hover {
    opacity: 0.8;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-15px);
    }
    60% {
        transform: translateY(-7px);
    }
}

/* Scroll down arrow styles */
.scroll-down-arrow {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 30;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(2, 54, 121, 0.3);
    border-radius: 50%;
    backdrop-filter: blur(4px);
    pointer-events: auto;
}

.scroll-down-arrow:hover {
    background-color: rgba(2, 54, 121, 0.5);
    transform: translateX(-50%) translateY(-5px);
}

@media (max-width: 768px) {
    .scroll-down-arrow {
        bottom: 2rem;
    }
}

/* Hero section responsive height adjustments */
@media screen and (max-height: 700px) {
    section.min-h-\[calc\(100vh-60px\)\] {
        min-height: 700px;
    }
}

@media screen and (max-width: 640px) {
    section.min-h-\[calc\(100vh-60px\)\] .container {
        padding-top: 6rem;
        padding-bottom: 6rem;
    }
}

/* Hero section responsive styles */
@media screen and (min-width: 1024px) {
    section.min-h-\[800px\] {
        height: calc(100vh - 60px);
        min-height: 800px;
        max-height: 1200px;
    }

    .hero-content {
        padding-top: 60px;
    }
}

@media screen and (max-width: 1023px) {
    section.min-h-\[800px\] {
        min-height: 700px;
    }
}

/* Adjust scroll arrow position for different screen sizes */
@media screen and (min-height: 900px) {
    .scroll-down-arrow {
        bottom: 4rem;
    }
}

@media screen and (max-height: 700px) {
    .scroll-down-arrow {
        bottom: 2rem;
    }
}

/* Hero section responsive styles */
@media screen and (min-width: 1024px) {
    section.min-h-\[600px\] {
        height: calc(100vh - 80px);
        min-height: 600px;
        max-height: 1000px;
    }
}

@media screen and (max-width: 1023px) {
    section.min-h-\[600px\] {
        min-height: 600px;
    }
    
    .scroll-down-arrow {
        bottom: 1.5rem;
    }
}