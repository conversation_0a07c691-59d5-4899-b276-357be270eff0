const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'kmuc fmkx oowi pnzu' // Potrebno generisati App Password u Google Account settings
  }
});

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { name, email, message } = req.body;

  const mailOptions = {
    from: '<EMAIL>',
    to: '<EMAIL>', // Možete dodati više email adresa ako želite
    subject: 'Nova poruka sa kontakt forme',
    text: `
      Ime: ${name}
      Email: ${email}
      Poruka: ${message}
    `
  };

  try {
    await transporter.sendMail(mailOptions);
    res.status(200).json({ message: 'Email uspešno poslat' });
  } catch (error) {
    console.error('<PERSON><PERSON><PERSON><PERSON> pri slanju emaila:', error);
    res.status(500).json({ message: '<PERSON><PERSON>ška pri slanju emaila' });
  }
}
